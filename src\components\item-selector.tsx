import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Search, Package, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Item, getAllItems, getRarityColor, getTypeIcon } from '@/lib/item-utils';

interface ItemSelectorProps {
  selectedItemId?: string;
  onSelectItem: (itemId: string | null) => void;
  itemType?: Item['type'];
  placeholder?: string;
  className?: string;
}

export function ItemSelector({
  selectedItemId,
  onSelectItem,
  itemType,
  placeholder = "Select an item...",
  className
}: ItemSelectorProps) {
  const [items, setItems] = useState<Item[]>([]);
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>(itemType || 'all');
  const [rarityFilter, setRarityFilter] = useState<string>('all');
  const [dialogOpen, setDialogOpen] = useState(false);

  const selectedItem = items.find(item => item.id === selectedItemId);

  useEffect(() => {
    loadItems();
  }, []);

  useEffect(() => {
    filterItems();
  }, [items, searchTerm, typeFilter, rarityFilter]);

  const loadItems = async () => {
    try {
      setLoading(true);
      const allItems = await getAllItems();
      setItems(allItems);
    } catch (error) {
      console.error('Error loading items:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterItems = () => {
    let filtered = [...items];    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (typeFilter !== 'all') {
      filtered = filtered.filter(item => item.type === typeFilter);
    }

    // Filter by rarity
    if (rarityFilter !== 'all') {
      filtered = filtered.filter(item => item.rarity === rarityFilter);
    }

    setFilteredItems(filtered);
  };
  const handleSelectItem = (item: Item) => {
    console.log(item)
    onSelectItem(item.id);
    setDialogOpen(false);
  };

  const clearSelection = () => {
    onSelectItem(null);
  };

  return (
    <div className={className}>
      <div className="flex gap-2">
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" className="flex-1 justify-start">
              {selectedItem ? (
                <div className="flex items-center gap-2 min-w-0">                  {selectedItem.image_url ? (
                  <img
                    src={selectedItem.image_url}
                    alt={selectedItem.name}
                    className="w-5 h-5 rounded object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                ) : (
                  <span className="text-sm">{getTypeIcon(selectedItem.type)}</span>
                )}
                  <span className="truncate">{selectedItem.name}</span>
                  <Badge variant="outline" className={cn('text-xs', getRarityColor(selectedItem.rarity))}>
                    {selectedItem.rarity}
                  </Badge>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-gray-500">
                  <Package className="h-4 w-4" />
                  <span>{placeholder}</span>
                </div>
              )}
            </Button>
          </DialogTrigger>

          <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <DialogTitle>Select Item</DialogTitle>
            </DialogHeader>

            {/* Filters */}
            <div className="space-y-4 border-b pb-4">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search items..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Type</Label>
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="avatar">Avatar</SelectItem>
                      <SelectItem value="avatar_border">Avatar Border</SelectItem>
                      <SelectItem value="background">Background</SelectItem>
                      <SelectItem value="title">Title</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Rarity</Label>
                  <Select value={rarityFilter} onValueChange={setRarityFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Rarities</SelectItem>
                      <SelectItem value="common">Common</SelectItem>
                      <SelectItem value="rare">Rare</SelectItem>
                      <SelectItem value="epic">Epic</SelectItem>
                      <SelectItem value="legendary">Legendary</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Items List */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
                </div>
              ) : filteredItems.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No items found</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-2">
                  {filteredItems.map((item) => (
                    <button
                      key={item.id}
                      onClick={() => handleSelectItem(item)} className={cn(
                        "flex items-center gap-3 p-3 rounded-lg border text-left transition-all hover:bg-gray-50",
                        selectedItemId === item.id && "ring-2 ring-amber-500 bg-amber-50"
                      )}
                    >
                      {/* Item Image or Icon */}
                      <div className="relative">                        {item.image_url ? (
                        <img
                          src={item.image_url}
                          alt={item.name}
                          className="w-10 h-10 rounded object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const fallback = target.nextElementSibling as HTMLElement;
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                      ) : null}
                        <div className={cn(
                          'w-10 h-10 flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200 rounded text-purple-600 text-xl',
                          item.image_url ? 'hidden' : 'flex'
                        )}
                        >
                          {getTypeIcon(item.type)}
                        </div>
                      </div>                      {/* Item Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
                          <Badge variant="outline" className={cn('text-xs', getRarityColor(item.rarity))}>
                            {item.rarity}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 capitalize">{item.type.replace('_', ' ')}</p>
                        {item.description && (
                          <p className="text-xs text-gray-500 mt-1 line-clamp-1">{item.description}</p>
                        )}
                        <p className="text-xs text-blue-600 mt-1">ID: {item.id}</p>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {selectedItem && (
          <Button variant="outline" size="icon" onClick={clearSelection} title="Clear selection">
            <Plus className="h-4 w-4 rotate-45" />
          </Button>
        )}
      </div>
    </div>
  );
}
