'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '../../../supabase/client';
import { useRouter } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import {
  Crown,
  Star,
  Gift,
  Zap,
  Calendar,
  Users,
  TrendingUp,
  Lock,
  Check
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import EnhancedBattlePassPanel from "@/components/enhanced-battle-pass-panel";
import { getCompleteBattlePassProgress } from "@/utils/battle-pass-utils";
import { Skeleton } from "@/components/ui/skeleton";

export default function BattlePassPage() {
  const [user, setUser] = useState<any>(null);
  const [playerData, setPlayerData] = useState<any>(null);
  const [battlePassProgress, setBattlePassProgress] = useState<any>(null);
  const [displayName, setDisplayName] = useState('');
  const [loading, setLoading] = useState(true);

  const supabase = createClient();
  const router = useRouter();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      const {
        data: { user: currentUser },
      } = await supabase.auth.getUser();

      if (!currentUser) {
        router.push("/sign-in");
        return;
      }

      setUser(currentUser);

      // Get user's player data
      const { data: userData } = await supabase
        .from('players')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      setPlayerData(userData);

      // Get battle pass progress
      const progressData = await getCompleteBattlePassProgress(currentUser.id);
      setBattlePassProgress(progressData);

      const name = userData?.display_name || currentUser.user_metadata?.full_name || 'Anonymous Player';
      setDisplayName(name);

    } catch (error) {
      console.error('Error loading battle pass data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <>
        <DashboardNavbar />
        <main className="w-full bg-gradient-to-br from-amber-50/60 via-orange-50/30 to-yellow-50/40 min-h-screen">
          <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 flex flex-col gap-6 sm:gap-8">
            {/* Header Skeleton */}
            <header className="text-center space-y-4">
              <div className="flex items-center justify-center gap-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-10 w-48" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
              <Skeleton className="h-6 w-96 mx-auto" />
            </header>

            {/* Info Cards Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="border-2 border-amber-200">
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-5 w-5" />
                      <Skeleton className="h-5 w-32" />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-20 rounded-full" />
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Battle Pass Panel Skeleton */}
            <Card className="border-2 border-amber-200">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Skeleton className="h-6 w-6" />
                  <Skeleton className="h-6 w-48" />
                </div>
                <Skeleton className="h-4 w-64" />
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full rounded-full" />
                </div>

                {/* Tier Progress Skeleton */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
                  {Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="bg-white rounded-lg border p-4 space-y-3">
                      <div className="text-center">
                        <Skeleton className="h-4 w-12 mx-auto mb-2" />
                        <Skeleton className="h-16 w-16 mx-auto rounded-lg" />
                      </div>
                      <Skeleton className="h-4 w-full" />
                      <div className="flex justify-between">
                        <Skeleton className="h-6 w-16 rounded-full" />
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Premium Battle Pass Promotion Skeleton */}
            <Card className="border-2 border-yellow-300 bg-gradient-to-r from-yellow-50 to-orange-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-7 w-56" />
                    </div>
                    <Skeleton className="h-4 w-80" />
                    <div className="flex gap-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <Skeleton key={index} className="h-4 w-20" />
                      ))}
                    </div>
                  </div>
                  <Skeleton className="h-12 w-40 rounded-lg" />
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </>
    );
  }

  if (!user || !playerData) {
    return null;
  }

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen relative">
        {/* Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-amber-100/20 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-blue-100/20 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-amber-100/10 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 py-8 flex flex-col gap-8 relative z-10">
          {/* Header Section */}
          <header className="text-center space-y-6">
            <div className="flex items-center justify-center gap-4">
              <div className="p-3 bg-amber-100 rounded-full shadow-lg">
                <Crown className="h-8 w-8 text-amber-600" />
              </div>
              <h1 className="text-5xl font-bold bg-gradient-to-r from-amber-700 via-amber-600 to-amber-800 bg-clip-text text-transparent font-serif">
                The Scholar's Journey
              </h1>
              <div className="p-3 bg-amber-100 rounded-full shadow-lg">
                <Crown className="h-8 w-8 text-amber-600" />
              </div>
            </div>
            <div className="max-w-3xl mx-auto">
              <p className="text-xl text-amber-800/80 font-medium mb-2">
                📚 Battle Pass 📚
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Embark on a literary adventure through tiers of knowledge. Each victory brings you closer to unlocking
                exclusive rewards and proving your mastery of the written word. Whether you choose the free path of learning
                or upgrade to premium scholarly privileges, every step forward is a testament to your dedication.
              </p>
            </div>
          </header>

          {/* Battle Pass Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Current Season */}
            <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-white shadow-lg hover:shadow-xl transition-shadow duration-300 border-2">
              <CardHeader className="pb-3 bg-gradient-to-r from-amber-100/50 to-transparent">
                <CardTitle className="flex items-center gap-3 text-amber-900 font-serif">
                  <div className="p-2 bg-amber-200 rounded-full">
                    <Calendar className="h-5 w-5" />
                  </div>
                  Current Chapter
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                {battlePassProgress ? (
                  <div className="space-y-3">
                    <h3 className="font-bold text-xl text-amber-900 font-serif">
                      📖 {battlePassProgress.season.season_name}
                    </h3>
                    <p className="text-sm text-amber-700 bg-amber-50 px-3 py-1 rounded-full inline-block">
                      📅 Ends: {new Date(battlePassProgress.season.end_date).toLocaleDateString()}
                    </p>
                    <Badge className={cn(
                      "text-sm font-medium",
                      battlePassProgress.playerProgress.has_premium
                        ? 'bg-gradient-to-r from-amber-200 to-amber-300 text-amber-900 border-amber-400'
                        : 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-900 border-blue-300'
                    )}>
                      {battlePassProgress.playerProgress.has_premium ? '👑 Premium Scholar' : '📚 Free Reader'}
                    </Badge>
                  </div>
                ) : (
                  <p className="text-amber-600 italic">No active chapter</p>
                )}
              </CardContent>
            </Card>

            {/* Current Progress */}
            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white shadow-lg hover:shadow-xl transition-shadow duration-300 border-2">
              <CardHeader className="pb-3 bg-gradient-to-r from-blue-100/50 to-transparent">
                <CardTitle className="flex items-center gap-3 text-blue-900 font-serif">
                  <div className="p-2 bg-blue-200 rounded-full">
                    <TrendingUp className="h-5 w-5" />
                  </div>
                  Your Journey
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                {battlePassProgress ? (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-blue-900 font-serif">
                        📜 Level {battlePassProgress.playerProgress.current_tier}
                      </span>
                      <span className="text-sm text-blue-700 bg-blue-50 px-3 py-1 rounded-full">
                        ✨ {battlePassProgress.playerProgress.current_xp.toLocaleString()} XP
                      </span>
                    </div>
                    <div className="space-y-2">
                      <Progress
                        value={battlePassProgress.progressPercentage}
                        className="h-3 bg-blue-100"
                      />
                      {battlePassProgress.nextTierInfo && (
                        <p className="text-xs text-blue-600 text-center bg-blue-50 px-2 py-1 rounded">
                          🎯 {battlePassProgress.xpToNextTier.toLocaleString()} XP until next milestone
                        </p>
                      )}
                    </div>
                  </div>
                ) : (
                  <p className="text-blue-600 italic">No journey data</p>
                )}
              </CardContent>
            </Card>

            {/* Rewards Info */}
            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white shadow-lg hover:shadow-xl transition-shadow duration-300 border-2">
              <CardHeader className="pb-3 bg-gradient-to-r from-purple-100/50 to-transparent">
                <CardTitle className="flex items-center gap-3 text-purple-900 font-serif">
                  <div className="p-2 bg-purple-200 rounded-full">
                    <Gift className="h-5 w-5" />
                  </div>
                  Treasure Awaits
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                {battlePassProgress ? (
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <div className="flex items-center gap-3 p-2 bg-blue-50 rounded-lg">
                        <Star className="h-5 w-5 text-blue-600" />
                        <span className="text-sm font-medium text-blue-900">📚 Free Scholar Path</span>
                      </div>
                      {battlePassProgress.playerProgress.has_premium && (
                        <div className="flex items-center gap-3 p-2 bg-amber-50 rounded-lg">
                          <Crown className="h-5 w-5 text-amber-600" />
                          <span className="text-sm font-medium text-amber-900">👑 Premium Scholar Path</span>
                        </div>
                      )}
                    </div>
                    <div className="text-center bg-purple-50 p-3 rounded-lg">
                      <p className="text-sm font-bold text-purple-900">
                        🎁 {battlePassProgress.tiers.length} Tiers of Rewards
                      </p>
                      <p className="text-xs text-purple-700 mt-1">
                        Unlock exclusive cosmetics, titles, and more!
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-purple-600 italic">No treasures available</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* How to Earn XP */}
          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white shadow-lg border-2">
            <CardHeader className="bg-gradient-to-r from-green-100/50 to-transparent">
              <CardTitle className="flex items-center gap-3 text-green-900 font-serif text-xl">
                <div className="p-2 bg-green-200 rounded-full">
                  <Zap className="h-6 w-6" />
                </div>
                📖 The Path to Wisdom
              </CardTitle>
              <CardDescription className="text-green-700 text-base">
                Embark on these scholarly pursuits to advance through the tiers of knowledge and unlock magnificent rewards
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Play Matches</h3>
                  <p className="text-xs text-gray-600">Earn XP for every match played</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Crown className="h-8 w-8 text-amber-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Win Games</h3>
                  <p className="text-xs text-gray-600">Bonus XP for victories</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <Star className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Daily Challenges</h3>
                  <p className="text-xs text-gray-600">Complete daily objectives</p>
                </div>
                <div className="text-center p-4 rounded-lg bg-white border border-green-100">
                  <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-sm">Achievements</h3>
                  <p className="text-xs text-gray-600">Unlock special milestones</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Battle Pass Panel */}
          <div className="space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold text-gray-900 font-serif">📜 Your Scholar's Progress 📜</h2>
              <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                Witness your journey through the tiers of knowledge. Each milestone brings new rewards and recognition of your scholarly achievements.
              </p>
            </div>
            <div className="bg-gradient-to-br from-amber-50/50 to-blue-50/50 p-6 rounded-2xl border-2 border-amber-200/50 shadow-lg">
              <EnhancedBattlePassPanel
                playerId={user.id}
                className="max-w-5xl mx-auto"
              />
            </div>
          </div>

          {/* Premium Upgrade CTA */}
          {battlePassProgress && !battlePassProgress.playerProgress.has_premium && (
            <Card className="border-amber-300 bg-gradient-to-r from-amber-100 via-amber-50 to-amber-100 max-w-3xl mx-auto shadow-xl border-2">
              <CardContent className="text-center py-10 px-8">
                <div className="mb-6">
                  <div className="inline-flex p-4 bg-amber-200 rounded-full shadow-lg mb-4">
                    <Crown className="h-12 w-12 text-amber-700" />
                  </div>
                  <h3 className="text-2xl font-bold text-amber-900 mb-3 font-serif">
                    👑 Ascend to Premium Scholar 👑
                  </h3>
                  <p className="text-amber-800 text-lg leading-relaxed">
                    Unlock the full potential of your scholarly journey! Access exclusive premium tiers,
                    rare cosmetic treasures, and accelerated progression through the halls of knowledge.
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-amber-800 mb-8">
                  <div className="flex flex-col items-center gap-2 p-4 bg-amber-50 rounded-lg">
                    <Check className="h-6 w-6 text-amber-600" />
                    <span className="font-semibold">🎁 Premium Rewards</span>
                    <span className="text-xs text-center">Exclusive items & cosmetics</span>
                  </div>
                  <div className="flex flex-col items-center gap-2 p-4 bg-amber-50 rounded-lg">
                    <Check className="h-6 w-6 text-amber-600" />
                    <span className="font-semibold">✨ Rare Collectibles</span>
                    <span className="text-xs text-center">Unique titles & borders</span>
                  </div>
                  <div className="flex flex-col items-center gap-2 p-4 bg-amber-50 rounded-lg">
                    <Check className="h-6 w-6 text-amber-600" />
                    <span className="font-semibold">⚡ Bonus Experience</span>
                    <span className="text-xs text-center">Faster tier progression</span>
                  </div>
                </div>
                <Button className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white text-lg px-8 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 font-semibold">
                  🚀 Upgrade to Premium Scholar
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
    </>
  );
};
