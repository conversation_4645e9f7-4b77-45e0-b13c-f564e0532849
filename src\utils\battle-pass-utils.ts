// Battle Pass utilities for managing seasons, tiers, and player progress
import { createClient } from "../../supabase/client";
import { populateItemRewards, type Reward } from '../lib/item-utils';

export interface BattlePassSeason {
  id: string;
  season_name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  created_at: string;
}

export interface BattlePassTier {
  id: string;
  season_id: string;
  tier_number: number;
  xp_required: number;
  reward_item_id: string | null;
  reward_xp: number | null;
  reward_currency: number | null;
  tier_type: 'free' | 'premium';
  created_at: string;
  // Computed reward object for compatibility
  reward?: Reward;
}

export interface PlayerBattlePass {
  player_id: string;
  season_id: string;
  current_tier: number;
  current_xp: number;
  has_premium: boolean;
  last_updated: string;
}

export interface BattlePassProgress {
  season: BattlePassSeason;
  playerProgress: PlayerBattlePass;
  tiers: BattlePassTier[];
  currentTierInfo: BattlePassTier | null;
  nextTierInfo: BattlePassTier | null;
  xpToNextTier: number;
  progressPercentage: number;
}

// Get current active battle pass season
export async function getCurrentSeason(): Promise<BattlePassSeason | null> {
  const supabase = createClient();
  
  const { data, error } = await supabase
    .from('battle_pass_seasons')
    .select('*')
    .eq('is_active', true)
    .single();
    
  if (error) {
    console.error('Error fetching current season:', error);
    return null;
  }
  
  return data;
}

// Get all tiers for a season
export async function getSeasonTiers(seasonId: string): Promise<BattlePassTier[]> {
  const supabase = createClient();
  
  // First get the tiers without the join
  const { data: tiersData, error } = await supabase
    .from('battle_pass_tiers')
    .select('*')
    .eq('season_id', seasonId)
    .order('tier_number', { ascending: true });
    
  if (error) {
    console.error('Error fetching season tiers:', error);
    return [];
  }

  if (!tiersData || tiersData.length === 0) {
    return [];
  }

  // Get all unique item IDs that we need to fetch
  const itemIds = tiersData
    .filter(tier => tier.reward_item_id)
    .map(tier => tier.reward_item_id);

  // Fetch the items if there are any
  let itemsMap = new Map();
  if (itemIds.length > 0) {
    const { data: itemsData } = await supabase
      .from('player_items')
      .select('id, name, type, rarity, image_url, description')
      .in('id', itemIds);
    
    if (itemsData) {
      itemsData.forEach(item => {
        itemsMap.set(item.id, item);
      });
    }
  }

  // Transform to include computed reward object for compatibility
  const transformedTiers = tiersData.map(tier => {
    let reward: Reward | null = null;
    const rewardItem = tier.reward_item_id ? itemsMap.get(tier.reward_item_id) : null;
    
    if (tier.reward_item_id && rewardItem) {
      reward = {
        type: 'item',
        id: rewardItem.id,
        item: rewardItem
      };
    } else if (tier.reward_xp) {
      reward = {
        type: 'xp',
        amount: tier.reward_xp
      };
    } else if (tier.reward_currency) {
      reward = {
        type: 'currency',
        amount: tier.reward_currency
      };
    }

    return {
      ...tier,
      reward
    };
  });

  return transformedTiers;
}

// Get player's battle pass progress for current season
export async function getPlayerBattlePassProgress(playerId: string): Promise<PlayerBattlePass | null> {
  const supabase = createClient();
  
  // First get current season
  const currentSeason = await getCurrentSeason();
  if (!currentSeason) return null;
  
  const { data, error } = await supabase
    .from('player_battle_pass')
    .select('*')
    .eq('player_id', playerId)
    .eq('season_id', currentSeason.id)
    .single();
    
  if (error) {
    // If no record exists, create one with default values
    if (error.code === 'PGRST116') {
      const newProgress = await createPlayerBattlePassProgress(playerId, currentSeason.id);
      return newProgress;
    }
    console.error('Error fetching player battle pass progress:', error);
    return null;
  }
  
  return data;
}

// Create initial battle pass progress for player
export async function createPlayerBattlePassProgress(playerId: string, seasonId: string): Promise<PlayerBattlePass | null> {
  const supabase = createClient();
  
  const newProgress = {
    player_id: playerId,
    season_id: seasonId,
    current_tier: 0,
    current_xp: 0,
    has_premium: false
  };
  
  const { data, error } = await supabase
    .from('player_battle_pass')
    .insert(newProgress)
    .select()
    .single();
    
  if (error) {
    console.error('Error creating player battle pass progress:', error);
    return null;
  }
  
  return data;
}

// Get complete battle pass progress for a player
export async function getCompleteBattlePassProgress(playerId: string): Promise<BattlePassProgress | null> {
  const currentSeason = await getCurrentSeason();
  if (!currentSeason) return null;
  
  const [playerProgress, tiers] = await Promise.all([
    getPlayerBattlePassProgress(playerId),
    getSeasonTiers(currentSeason.id)
  ]);
  
  if (!playerProgress) return null;
  
  const currentTierInfo = tiers.find(tier => tier.tier_number === playerProgress.current_tier) || null;
  const nextTierInfo = tiers.find(tier => tier.tier_number === playerProgress.current_tier + 1) || null;
  
  const xpToNextTier = nextTierInfo ? nextTierInfo.xp_required - playerProgress.current_xp : 0;
  const progressPercentage = currentTierInfo && nextTierInfo 
    ? ((playerProgress.current_xp - currentTierInfo.xp_required) / (nextTierInfo.xp_required - currentTierInfo.xp_required)) * 100
    : 0;
  
  return {
    season: currentSeason,
    playerProgress,
    tiers,
    currentTierInfo,
    nextTierInfo,
    xpToNextTier,
    progressPercentage: Math.max(0, Math.min(100, progressPercentage))
  };
}

// Add XP to player's battle pass
export async function addBattlePassXP(playerId: string, xpAmount: number): Promise<boolean> {
  const supabase = createClient();
  
  const progress = await getPlayerBattlePassProgress(playerId);
  if (!progress) return false;
  
  const newXP = progress.current_xp + xpAmount;
  
  // Get tiers to check for tier ups
  const tiers = await getSeasonTiers(progress.season_id);
  let newTier = progress.current_tier;
  
  // Check if player should tier up
  for (const tier of tiers) {
    if (newXP >= tier.xp_required && tier.tier_number > newTier) {
      newTier = tier.tier_number;
    }
  }
  
  const { error } = await supabase
    .from('player_battle_pass')
    .update({
      current_xp: newXP,
      current_tier: newTier,
      last_updated: new Date().toISOString()
    })
    .eq('player_id', playerId)
    .eq('season_id', progress.season_id);
    
  if (error) {
    console.error('Error updating battle pass XP:', error);
    return false;
  }
  
  return true;
}

// Check if player has premium battle pass
export async function hasPlayerPremiumBattlePass(playerId: string): Promise<boolean> {
  const progress = await getPlayerBattlePassProgress(playerId);
  return progress?.has_premium || false;
}

// Upgrade player to premium battle pass
export async function upgradeToPremiumBattlePass(playerId: string): Promise<boolean> {
  const supabase = createClient();

  const progress = await getPlayerBattlePassProgress(playerId);
  if (!progress) return false;

  const { error } = await supabase
    .from('player_battle_pass')
    .update({
      has_premium: true,
      last_updated: new Date().toISOString()
    })
    .eq('player_id', playerId)
    .eq('season_id', progress.season_id);

  if (error) {
    console.error('Error upgrading to premium battle pass:', error);
    return false;
  }

  return true;
}

// Seed default battle pass season and tiers
export async function seedDefaultBattlePass(): Promise<{ success: boolean; message: string }> {
  const supabase = createClient();

  try {
    // Check if there's already an active season
    const existingSeason = await getCurrentSeason();
    if (existingSeason) {
      return {
        success: true,
        message: 'Active battle pass season already exists'
      };
    }

    // Create new season
    const seasonEndDate = new Date();
    seasonEndDate.setMonth(seasonEndDate.getMonth() + 3); // 3 months from now

    const { data: newSeason, error: seasonError } = await supabase
      .from('battle_pass_seasons')
      .insert({
        season_name: 'Season 1: The Scribe\'s Journey',
        start_date: new Date().toISOString(),
        end_date: seasonEndDate.toISOString(),
        is_active: true
      })
      .select()
      .single();

    if (seasonError || !newSeason) {
      console.error('Error creating season:', seasonError);
      return {
        success: false,
        message: 'Failed to create battle pass season'
      };
    }

    // Create tiers for the season
    const tiers = [];
    for (let i = 0; i <= 20; i++) {
      const xpRequired = i === 0 ? 0 : Math.floor(100 * Math.pow(1.2, i - 1));

      // Create alternating free and premium tiers
      const isFree = i % 2 === 1; // Odd tiers are free, even tiers are premium

      tiers.push({
        season_id: newSeason.id,
        tier_number: i,
        xp_required: xpRequired,
        reward: i > 0 ? (isFree ? {
          type: i % 5 === 0 ? 'item' : 'xp',
          amount: i % 5 === 0 ? undefined : 50,
          item_id: i % 5 === 0 ? (i === 5 ? 'avatar_wizard' : i === 10 ? 'title_lexicon_master' : i === 15 ? 'avatar_sage' : i === 20 ? 'border_gold' : 'avatar_scholar') : undefined
        } : {
          type: i % 3 === 0 ? 'item' : 'currency',
          amount: i % 3 === 0 ? undefined : 100,
          item_id: i % 3 === 0 ? (i === 3 ? 'border_bronze' : i === 6 ? 'bg_library' : i === 9 ? 'border_silver' : i === 12 ? 'bg_castle' : i === 18 ? 'bg_starfield' : i === 21 ? 'title_word_sage' : 'title_wordsmith') : undefined        }) : null,
        tier_type: isFree ? 'free' : 'premium'
      });
    }

    const { error: tiersError } = await supabase
      .from('battle_pass_tiers')
      .insert(tiers);

    if (tiersError) {
      console.error('Error creating tiers:', tiersError);
      return {
        success: false,
        message: 'Failed to create battle pass tiers'
      };
    }

    return {
      success: true,
      message: `Successfully created battle pass season with ${tiers.length} tiers`
    };

  } catch (error) {
    console.error('Error seeding battle pass:', error);
    return {
      success: false,
      message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

export const checkUnlockRequirement = (requirement: string | { type: string; value: number | string } | null, isPremium: boolean, playerLevel: any, playerRP: any, hasBattlePass: any, battlePassTier: any): { unlocked: boolean; message: string; canUnlock: boolean } => {
  if (!requirement) return { unlocked: true, message: 'No requirements', canUnlock: false };

  let req: { type: string; value: number | string };
  
  // Handle both string (JSON) and object formats
  if (typeof requirement === 'string') {
    try {
      req = JSON.parse(requirement);
    } catch {
      return { unlocked: true, message: requirement, canUnlock: false };
    }
  } else {
    req = requirement;
  }

  const type = req.type;
  const value = req.value;
  
  switch (type) {
    case 'level':
      const levelUnlocked = playerLevel >= (value as number);
      return {
        unlocked: levelUnlocked,
        message: `Requires Level ${value}`,
        canUnlock: !levelUnlocked && playerLevel >= ((value as number) - 5) // Allow unlock if close to requirement
      };
    case 'rp':
      const rpUnlocked = playerRP >= (value as number);
      return {
        unlocked: rpUnlocked,
        message: `Requires ${value} RP`,
        canUnlock: !rpUnlocked && playerRP >= ((value as number) * 0.8) // Allow unlock if 80% of RP requirement met
      };
    case 'battlepass':
      return {
        unlocked: hasBattlePass,
        message: `Requires Battle Pass Tier ${value}`,
        canUnlock: isPremium ? hasBattlePass && battlePassTier >= (value as number) : battlePassTier >= (value as number) // Allow unlock if close to tier
      };
    case 'achievement':
      return {
        unlocked: false, // Placeholder for achievement system
        message: `Requires achievement: ${value}`,
        canUnlock: false
      };
    default:
      return { unlocked: true, message: 'Unknown requirement', canUnlock: false };
  }
};