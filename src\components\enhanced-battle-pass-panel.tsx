'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Lock, Check, Crown, Star, Gift, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createClient } from '../../supabase/client';
import {
  BattlePassProgress,
  BattlePassTier,
  getCompleteBattlePassProgress,
  upgradeToPremiumBattlePass
} from '@/utils/battle-pass-utils';
import {
  equipItem,
  unlockItem,
  hasUnlockedItem,
  getPlayerInventory,
  type PlayerItem
} from '@/utils/customization-utils';
import { RewardDisplay } from '@/components/reward-display';
import { RewardPreviewModal } from '@/components/reward-preview-modal';

interface EnhancedBattlePassPanelProps {
  playerId: string;
  className?: string;
}

interface PlayerData {
  id: string;
  level_experience: number;
  rankpoints: number;
  equipped_items: {
    avatar?: string;
    avatar_border?: string;
    background?: string;
    title?: string;
  };
}

export default function EnhancedBattlePassPanel({ playerId, className }: EnhancedBattlePassPanelProps) {
  const [battlePassData, setBattlePassData] = useState<BattlePassProgress | null>(null);
  const [playerData, setPlayerData] = useState<PlayerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(false);
  const [unlockedItems, setUnlockedItems] = useState<Set<string>>(new Set());
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [selectedReward, setSelectedReward] = useState<any>(null);
  const [claimAnimation, setClaimAnimation] = useState<{
    show: boolean;
    reward: any;
    position: { x: number; y: number };
  }>({ show: false, reward: null, position: { x: 0, y: 0 } });
  useEffect(() => {
    loadBattlePassData();
    loadPlayerData();
    loadUnlockedItems();

    // Set up real-time subscription for unlocked items
    const supabase = createClient();
    const subscription = supabase
      .channel('player_inventory_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'player_item_inventory',
          filter: `player_id=eq.${playerId}`
        },
        (payload) => {
          console.log('Inventory change detected:', payload);
          handleInventoryChange(payload);
        }
      )
      .subscribe();

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [playerId]);

  const loadPlayerData = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('players')
        .select(`
          id,
          level_experience,
          rankpoints,
          equipped_avatar_id,
          equipped_avatar_border_id,
          equipped_background_id,
          equipped_title_id
        `)
        .eq('id', playerId)
        .single();

      if (error) {
        console.error('Error fetching player data:', error);
        return;
      }

      setPlayerData({
        id: data.id,
        level_experience: data.level_experience || 0,
        rankpoints: data.rankpoints || 0,
        equipped_items: {
          avatar: data.equipped_avatar_id,
          avatar_border: data.equipped_avatar_border_id,
          background: data.equipped_background_id,
          title: data.equipped_title_id,
        }
      });
    } catch (error) {
      console.error('Error loading player data:', error);
    }
  };
  const loadUnlockedItems = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('player_item_inventory')
        .select('item_id')
        .eq('player_id', playerId);

      if (error) {
        console.error('Error loading unlocked items:', error);
        return;
      }

      const itemIds = new Set(data?.map(item => item.item_id) || []);
      setUnlockedItems(itemIds);
    } catch (error) {
      console.error('Error loading unlocked items:', error);
    }
  };
  const handleInventoryChange = (payload: any) => {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    console.log('Real-time inventory change:', { eventType, newRecord, oldRecord });

    switch (eventType) {
      case 'INSERT':
        // Item was unlocked
        if (newRecord?.item_id) {
          setUnlockedItems(prev => {
            const newSet = new Set(prev);
            newSet.add(newRecord.item_id);
            console.log('Item added to inventory via real-time:', newRecord.item_id);
            return newSet;
          });
        }
        break;

      case 'DELETE':
        // Item was removed (rare case)
        if (oldRecord?.item_id) {
          setUnlockedItems(prev => {
            const newSet = new Set(prev);
            newSet.delete(oldRecord.item_id);
            console.log('Item removed from inventory via real-time:', oldRecord.item_id);
            return newSet;
          });
        }
        break;

      case 'UPDATE':
        // Handle any updates if needed
        console.log('Inventory update detected, but no action needed');
        break;

      default:
        console.log('Unknown event type:', eventType);
    }
  };

  const loadBattlePassData = async () => {
    setLoading(true);
    try {
      const data = await getCompleteBattlePassProgress(playerId);
      setBattlePassData(data);
      console.log(data)
    } catch (error) {
      console.error('Error loading battle pass data:', error);
      toast.error('Failed to load battle pass data');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeToPremium = async () => {
    if (!battlePassData) return;

    setUpgrading(true);
    try {
      const success = await upgradeToPremiumBattlePass(playerId);
      if (success) {
        toast.success('Upgraded to Premium Battle Pass!');
        await loadBattlePassData();
      } else {
        toast.error('Failed to upgrade to premium');
      }
    } catch (error) {
      console.error('Error upgrading to premium:', error);
      toast.error('Failed to upgrade to premium');
    } finally {
      setUpgrading(false);
    }
  };

  const handleEquipItem = async (itemId: string, itemType: string) => {
    try {
      const success = await equipItem(playerId, itemId, itemType as any);
      if (success) {
        toast.success('Item equipped successfully!');
        await loadPlayerData();
      } else {
        toast.error('Failed to equip item');
      }
    } catch (error) {
      console.error('Error equipping item:', error);
      toast.error('Failed to equip item');
    }
  };
  const handleUnlockItem = async (itemId: string) => {
    try {
      const success = await unlockItem(playerId, itemId);
      if (success) {
        // Only update optimistically after successful server response
        setUnlockedItems(prev => {
          const newSet = new Set(prev);
          newSet.add(itemId);
          return newSet;
        });
        toast.success('Item unlocked successfully!');
        // The real-time subscription will also handle the update for consistency
      } else {
        toast.error('Failed to unlock item');
      }
    } catch (error) {
      console.error('Error unlocking item:', error);
      toast.error('Failed to unlock item');
    }
  }; const handleClaimReward = async (tier: any, event?: React.MouseEvent) => {
    if (!tier.reward) return;

    // For item rewards, unlock the item
    if (tier.reward.type === 'item' && (tier.reward.item_id || tier.reward.id)) {
      const itemId = tier.reward.item_id || tier.reward.id;

      // Show claim animation for item rewards
      if (event && tier.reward.type === 'item') {
        const rect = (event.target as HTMLElement).getBoundingClientRect();
        setClaimAnimation({
          show: true,
          reward: tier.reward,
          position: {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
          }
        });

        // Hide animation after 2 seconds
        setTimeout(() => {
          setClaimAnimation({ show: false, reward: null, position: { x: 0, y: 0 } });
        }, 2000);
      }

      await handleUnlockItem(itemId);
    } else {
      // For other reward types (XP, currency, etc.), handle accordingly
      toast.success('Reward claimed successfully!');
    }
  };

  const handlePreviewItem = (itemId: string, itemType: string) => {
    // This would integrate with a preview system
    console.log('Preview item:', itemId, itemType);
  }; const handleRewardClick = (reward: any, isRewardClaimed: boolean, tier?: any) => {
    if (reward && reward.type === 'item' && battlePassData) {
      const isPremiumTier = tier?.tier_type === 'premium';
      const canAccessTier = !isPremiumTier || battlePassData.playerProgress.has_premium;

      setSelectedReward({
        ...reward,
        isUnlocked: isRewardClaimed && canAccessTier,
        isEquipped: playerData?.equipped_items && reward.item_id ?
          Object.values(playerData.equipped_items).includes(reward.item_id) : false,
        isPremiumLocked: isPremiumTier && !battlePassData.playerProgress.has_premium
      });
      setShowRewardModal(true);
    }
  };

  const isItemUnlocked = (itemId: string): boolean => {
    return unlockedItems.has(itemId);
  };

  const isItemEquipped = (itemId: string): boolean => {
    if (!playerData) return false;
    const equippedItems = playerData.equipped_items;
    return Object.values(equippedItems).includes(itemId);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-700"></div>
        </CardContent>
      </Card>
    );
  }

  if (!battlePassData) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">No active battle pass season</p>
        </CardContent>
      </Card>
    );
  }

  const { season, playerProgress, tiers, currentTierInfo, nextTierInfo, xpToNextTier, progressPercentage } = battlePassData;

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-amber-600" />
                {season.season_name}
              </CardTitle>
              <CardDescription>
                Battle Pass • Tier {playerProgress.current_tier} / {tiers.length - 1}
              </CardDescription>
            </div>
            {!playerProgress.has_premium && (
              <Button
                onClick={handleUpgradeToPremium}
                disabled={upgrading}
                className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
              >
                {upgrading ? 'Upgrading...' : 'Upgrade'}
              </Button>
            )}
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Current Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Current XP: {playerProgress.current_xp.toLocaleString()}</span>
              {nextTierInfo && (
                <span>Next Tier: {xpToNextTier.toLocaleString()} XP</span>
              )}
            </div>
            <Progress
              value={progressPercentage}
              className="h-3"
            />
          </div>          {/* Tier Rewards */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Tier Rewards</h3>
            <div className="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">              {tiers.map((tier) => {
              const tierUnlocked = playerProgress.current_tier >= tier.tier_number;
              const isCurrent = playerProgress.current_tier === tier.tier_number;
              const isPremiumTier = tier.tier_type === 'premium';
              const canAccessTier = !isPremiumTier || playerProgress.has_premium;                // Check if the specific item is already claimed/unlocked
              const hasRewardAlready = tier.reward?.type === 'item' &&
                (tier.reward.item_id || tier.reward.id) &&
                isItemUnlocked(tier.reward.item_id || tier.reward.id);

              // Player can claim if they have enough XP, can access the tier, and haven't claimed the reward yet
              const canClaim = playerProgress.current_xp >= tier.xp_required && canAccessTier && !hasRewardAlready;

              // Item is considered "unlocked" if the tier is unlocked AND the item is actually in inventory
              const isRewardClaimed = Boolean(hasRewardAlready);

              return (
                <div
                  key={tier.id} className={cn(
                    'flex items-center gap-4 p-3 rounded-lg border',
                    isCurrent && 'ring-2 ring-amber-500 bg-amber-50',
                    isRewardClaimed && !isCurrent && 'bg-green-50 border-green-200',
                    !isRewardClaimed && canClaim && 'bg-blue-50 border-blue-200',
                    !isRewardClaimed && !canClaim && 'bg-gray-50 border-gray-200'
                  )}
                >
                  {/* Tier Number */}                    <div className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                    isCurrent && 'bg-amber-500 text-white',
                    isRewardClaimed && !isCurrent && 'bg-green-500 text-white',
                    canClaim && !isRewardClaimed && 'bg-blue-500 text-white',
                    !isRewardClaimed && !canClaim && 'bg-gray-300 text-gray-600'
                  )}>
                    {isRewardClaimed ? <Check size={14} /> : tier.tier_number}
                  </div>

                  {/* Reward Display */}
                  <div className="flex-1">                      <div
                    className="cursor-pointer relative"
                    onClick={() => tier.reward && handleRewardClick(tier.reward, isRewardClaimed, tier)}
                  >
                    {tier.reward ? (
                      <div className="relative">
                        <RewardDisplay
                          tier={tier}
                          reward={tier.reward}
                          isPremium={isPremiumTier}
                          size="sm"
                          showDescription={false}
                          className={cn(
                            "hover:opacity-80 transition-colors",
                            tier.tier_type === 'free'
                              ? 'bg-blue-50 border-blue-200 hover:bg-blue-100'
                              : 'bg-amber-50 border-amber-200 hover:bg-amber-100',
                            !canAccessTier && 'opacity-75'
                          )}
                          clickable={false}
                          playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
                          playerRP={playerData?.rankpoints || 0}
                          hasBattlePass={playerProgress.has_premium} battlePassTier={playerProgress.current_tier}
                          isUnlocked={isRewardClaimed}
                          onUnlock={(itemId) => handleUnlockItem(itemId)}
                          isItemUnlocked={(itemId) => isItemUnlocked(itemId)}
                        />
                        {/* Premium Lock Overlay */}
                        {isPremiumTier && !playerProgress.has_premium && (
                          <div className="absolute top-1 right-1 bg-amber-500 text-white rounded-full p-1">
                            <Lock size={10} />
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="bg-gray-100 border border-gray-200 rounded-lg p-3 flex items-center justify-center">
                        <div className="flex items-center gap-2 text-gray-500">
                          <Gift size={16} />
                          <span className="text-xs">No Reward</span>
                        </div>
                      </div>
                    )}
                  </div>

                    {/* Tier Type Badge */}
                    <div className="mt-2 flex justify-center">
                      <Badge
                        variant={tier.tier_type === 'free' ? 'secondary' : 'default'}
                        className={cn(
                          "text-xs",
                          tier.tier_type === 'free'
                            ? 'bg-blue-100 text-blue-800 border-blue-300'
                            : 'bg-amber-100 text-amber-800 border-amber-300'
                        )}
                      >
                        {tier.tier_type === 'free' ? 'Free' : 'Premium'}
                      </Badge>
                    </div>
                  </div>

                  {/* Claim Button or XP Required */}
                  <div className="text-right flex flex-col items-end gap-2">
                    <p className="text-xs text-gray-500">
                      {tier.xp_required.toLocaleString()} XP
                    </p>                      {/* Claim Button Logic */}
                    {canClaim && (
                      <Button
                        size="sm"
                        onClick={(e) => handleClaimReward(tier, e)}
                        className="h-6 px-2 text-xs bg-blue-500 hover:bg-blue-600"
                      >
                        Claim
                      </Button>
                    )}

                    {/* Status Indicators */}
                    {isRewardClaimed && (
                      <span className="text-xs text-green-600 font-medium">Claimed</span>
                    )}

                    {isPremiumTier && !playerProgress.has_premium && (
                      <span className="text-xs text-amber-600 font-medium">Premium</span>
                    )}
                  </div>
                </div>
              );
            })}
            </div>
          </div>

          {/* Season Info */}
          <div className="pt-4 border-t border-gray-200">
            <div className="flex justify-between text-xs text-gray-500">
              <span>Season ends: {new Date(season.end_date).toLocaleDateString()}</span>
              <span>{playerProgress.has_premium ? 'Premium' : 'Free'} Pass</span>
            </div>
          </div>
        </CardContent>
      </Card>      {/* Enhanced Reward Preview Modal */}
      {selectedReward && (
        <RewardPreviewModal
          reward={selectedReward}
          isOpen={showRewardModal}
          showRequirements={false}
          onClose={() => {
            setShowRewardModal(false);
            setSelectedReward(null);
          }}
          playerLevel={playerData ? Math.floor(Math.sqrt(playerData.level_experience / 50)) + 1 : 1}
          playerRP={playerData?.rankpoints || 0}
          hasBattlePass={playerProgress.has_premium}
          battlePassTier={playerProgress.current_tier}
          isUnlocked={selectedReward.isUnlocked && !selectedReward.isPremiumLocked}
          isEquipped={selectedReward.isEquipped}
          onEquip={selectedReward.item_id && !selectedReward.isPremiumLocked ?
            () => handleEquipItem(selectedReward.item_id, selectedReward.item.type) : undefined}
          onPreview={selectedReward.item_id ?
            () => handlePreviewItem(selectedReward.item_id, selectedReward.item.type) : undefined}
        />)}      
        {/* Claim Animation Popup */}
      {claimAnimation.show && claimAnimation.reward && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: claimAnimation.position.x - 50,
            top: claimAnimation.position.y - 50,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="animate-bounce bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg p-4 shadow-xl border-2 border-green-600">
            <div className="flex items-center gap-3">
              <div className="bg-white bg-opacity-20 rounded-full p-2">
                <Check className="h-5 w-5" />
              </div>
              <div>
                <div className="text-sm font-bold">Item Claimed!</div>
                <div className="text-xs opacity-90">
                  {claimAnimation.reward.item?.name || claimAnimation.reward.item_id || 'Reward'}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
